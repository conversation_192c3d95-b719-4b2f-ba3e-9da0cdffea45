# CI/CD Pipeline Documentation

This project includes automated CI/CD pipelines for building, testing, and deploying Docker images with automatic semantic versioning.

## Overview

The CI/CD system consists of two main workflows:

1. **Automatic CI/CD Pipeline** (`.github/workflows/ci-cd.yml`)
2. **Manual Release Pipeline** (`.github/workflows/manual-release.yml`)

## Automatic Versioning

The automatic pipeline uses **semantic versioning** based on commit message conventions:

### Commit Message Conventions

- **Major version bump** (e.g., 1.0.0 → 2.0.0):
  - `feat!: breaking change description`
  - `fix!: breaking change description`
  - Any commit with `BREAKING CHANGE` in the body

- **Minor version bump** (e.g., 1.0.0 → 1.1.0):
  - `feat: new feature description`
  - `feature: new feature description`

- **Patch version bump** (e.g., 1.0.0 → 1.0.1):
  - `fix: bug fix description`
  - `bugfix: bug fix description`
  - `patch: patch description`
  - Any other commit type

### Examples

```bash
# Patch release (1.0.0 → 1.0.1)
git commit -m "fix: resolve database connection timeout"

# Minor release (1.0.0 → 1.1.0)
git commit -m "feat: add new data validation feature"

# Major release (1.0.0 → 2.0.0)
git commit -m "feat!: redesign API with breaking changes"
```

## Workflows

### Automatic CI/CD Pipeline

**Triggers:**
- Push to `main` branch
- Push to `develop` branch
- Pull requests to `main` branch
- Manual trigger via GitHub Actions UI

**Steps:**
1. **Test**: Runs tests (currently placeholder)
2. **Version**: Determines version bump based on commit messages
3. **Build & Push**: Builds Docker image and pushes to GitHub Container Registry
4. **Release**: Creates GitHub release with changelog

**Docker Images:**
- Registry: `ghcr.io/your-username/zombie-ip-s3-ingest`
- Tags: `latest`, `v1.2.3`, `1.2.3`, `1.2`, `1`

### Manual Release Pipeline

**Triggers:**
- Manual trigger via GitHub Actions UI

**Inputs:**
- Version number (e.g., `1.2.3`)
- Release type: `release`, `prerelease`, or `hotfix`

**Use Cases:**
- Emergency hotfixes
- Specific version releases
- Pre-releases for testing

## Setup Requirements

### 1. GitHub Container Registry

The workflows are configured to use GitHub Container Registry (GHCR). No additional setup is required as it uses the built-in `GITHUB_TOKEN`.

### 2. Repository Settings

Ensure the following repository settings:

1. **Actions permissions**: Allow GitHub Actions to create and approve pull requests
2. **Package permissions**: Allow Actions to write packages

### 3. Branch Protection (Recommended)

Set up branch protection rules for the `main` branch:
- Require pull request reviews
- Require status checks to pass
- Require branches to be up to date

## Docker Image Usage

### Pull Latest Image

```bash
docker pull ghcr.io/your-username/zombie-ip-s3-ingest:latest
```

### Pull Specific Version

```bash
docker pull ghcr.io/your-username/zombie-ip-s3-ingest:v1.2.3
```

### Run Container

```bash
docker run -d \
  --name zombie-ip-ingest \
  -e DATABASE_URL="your-database-url" \
  -e AWS_ACCESS_KEY_ID="your-access-key" \
  -e AWS_SECRET_ACCESS_KEY="your-secret-key" \
  ghcr.io/your-username/zombie-ip-s3-ingest:latest
```

## Version Management

The application version is managed in `src/zombie_ip_ingest/version.py`. The CI/CD pipeline automatically updates tags and releases, but the version file serves as the source of truth for the application.

## Monitoring Releases

- **GitHub Releases**: View all releases at `https://github.com/your-username/zombie-ip-s3-ingest/releases`
- **Container Registry**: View images at `https://github.com/your-username/zombie-ip-s3-ingest/pkgs/container/zombie-ip-s3-ingest`
- **Actions**: Monitor workflow runs at `https://github.com/your-username/zombie-ip-s3-ingest/actions`

## Troubleshooting

### Common Issues

1. **Version not bumping**: Check commit message format
2. **Docker build fails**: Check Dockerfile syntax and dependencies
3. **Permission denied**: Verify repository and package permissions

### Debug Steps

1. Check workflow logs in GitHub Actions
2. Verify commit message conventions
3. Ensure all required secrets are configured
4. Check Docker build context and dependencies

## Customization

### Changing Version Bump Logic

Edit the version determination logic in `.github/workflows/ci-cd.yml` around line 50-80.

### Adding Different Registries

Modify the `REGISTRY` environment variable and login action in both workflow files.

### Custom Release Notes

Modify the changelog generation logic in the `create-release` job.
