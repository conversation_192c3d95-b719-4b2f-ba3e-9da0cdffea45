services:
  zombie-ip-ingest:
    build:
      context: .
      dockerfile: Dockerfile
    image: ghcr.io/data443/zombie-ip-s3-ingest:0.0.3
    container_name: zombie-ip-ingest
    restart: unless-stopped
    
    # Environment variables - copy from .env file or set directly
    environment:
      # AWS Configuration
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_REGION=${AWS_REGION:-us-east-1}
      
      # S3 Configuration
      - S3_BUCKET_NAME=${S3_BUCKET_NAME}
      - S3_PREFIX=${S3_PREFIX:-}
      
      # Processing Configuration
      - FILE_BATCH_SIZE=${FILE_BATCH_SIZE:-10}
      - MAX_WORKERS=${MAX_WORKERS:-4}
      - ENABLE_PARALLEL_PROCESSING=${ENABLE_PARALLEL_PROCESSING:-true}
      
      # Error Handling Configuration
      - MAX_RETRIES=${MAX_RETRIES:-3}
      - RETRY_DELAY_SECONDS=${RETRY_DELAY_SECONDS:-5}
      - CONTINUE_ON_ERROR=${CONTINUE_ON_ERROR:-true}
      
      # MSSQL Database Configuration
      - MSSQL_SERVER=${MSSQL_SERVER}
      - MSSQL_DATABASE=${MSSQL_DATABASE:-IpRepDB}
      - MSSQL_USERNAME=${MSSQL_USERNAME}
      - MSSQL_PASSWORD=${MSSQL_PASSWORD}
      - MSSQL_DRIVER=${MSSQL_DRIVER:-ODBC Driver 17 for SQL Server}
      - SCRIPT_ID=${SCRIPT_ID:-1001}
    
    # Load environment variables from .env file
    env_file:
      - .env
    
    # Volumes for logs or persistent data (optional)
    volumes:
      - ./logs:/app/logs:rw
    
    # Resource limits (optional)
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
    
    # Health check (optional)
    healthcheck:
      test: ["CMD", "python", "-c", "import sys; sys.exit(0)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # Logging configuration
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    
    # Network configuration (if needed)
    networks:
      - zombie-ip-network

networks:
  zombie-ip-network:
    driver: bridge
