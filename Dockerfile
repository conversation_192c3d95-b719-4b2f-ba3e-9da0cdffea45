# syntax=docker/dockerfile:1

FROM python:3.11-slim AS base

# Build arguments
ARG VERSION=dev
ENV APP_VERSION=${VERSION}

# Labels for metadata
LABEL org.opencontainers.image.version="${VERSION}"
LABEL org.opencontainers.image.title="Zombie IP S3 Ingest"
LABEL org.opencontainers.image.description="Application for ingesting Zombie IP data from S3"

# Set noninteractive installation
ENV DEBIAN_FRONTEND=noninteractive

# Fix potential signature issues
RUN apt-get clean && \
    rm -rf /var/lib/apt/lists/* && \
    apt-get update --fix-missing

# Install ODBC Driver and dependencies
RUN apt-get install -y --no-install-recommends \
    gnupg \
    curl \
    apt-transport-https \
    unixodbc \
    unixodbc-dev \
    python3 \
    python3-pip \
    python3-venv \
    && curl https://packages.microsoft.com/keys/microsoft.asc | gpg --dearmor > /usr/share/keyrings/microsoft-archive-keyring.gpg \
    && echo "deb [arch=amd64 signed-by=/usr/share/keyrings/microsoft-archive-keyring.gpg] https://packages.microsoft.com/ubuntu/22.04/prod jammy main" > /etc/apt/sources.list.d/mssql-release.list \
    && apt-get update \
    && ACCEPT_EULA=Y apt-get install -y --no-install-recommends msodbcsql17 \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app


# Copy requirements and install dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create non-root user for security
RUN groupadd -r appuser && useradd -r -g appuser appuser \
    && chown -R appuser:appuser /app

# Set permissions for entrypoint script
RUN chmod +x docker-entrypoint.sh

USER appuser

# Set entrypoint
ENTRYPOINT ["/app/docker-entrypoint.sh"]

# Default command
CMD ["python", "-m", "main"]
