#!/bin/bash

# Version management script for Zombie IP S3 Ingest

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
VERSION_FILE="$PROJECT_ROOT/src/zombie_ip_ingest/version.py"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

usage() {
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  current                 Show current version"
    echo "  bump [major|minor|patch] Bump version"
    echo "  set <version>           Set specific version"
    echo "  tag                     Create git tag for current version"
    echo ""
    echo "Examples:"
    echo "  $0 current"
    echo "  $0 bump patch"
    echo "  $0 set 1.2.3"
    echo "  $0 tag"
}

get_current_version() {
    grep -E '^__version__' "$VERSION_FILE" | sed 's/__version__ = "\(.*\)"/\1/'
}

set_version() {
    local new_version="$1"
    
    # Validate semantic version format
    if [[ ! $new_version =~ ^[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
        echo -e "${RED}Error: Version must be in semantic version format (e.g., 1.2.3)${NC}"
        exit 1
    fi
    
    # Update version file
    sed -i.bak "s/__version__ = \".*\"/__version__ = \"$new_version\"/" "$VERSION_FILE"
    rm "$VERSION_FILE.bak"
    
    echo -e "${GREEN}Version updated to $new_version${NC}"
}

bump_version() {
    local bump_type="$1"
    local current_version
    current_version=$(get_current_version)
    
    # Split version into parts
    IFS='.' read -ra VERSION_PARTS <<< "$current_version"
    local major=${VERSION_PARTS[0]}
    local minor=${VERSION_PARTS[1]}
    local patch=${VERSION_PARTS[2]}
    
    case $bump_type in
        major)
            major=$((major + 1))
            minor=0
            patch=0
            ;;
        minor)
            minor=$((minor + 1))
            patch=0
            ;;
        patch)
            patch=$((patch + 1))
            ;;
        *)
            echo -e "${RED}Error: Invalid bump type. Use major, minor, or patch${NC}"
            exit 1
            ;;
    esac
    
    local new_version="$major.$minor.$patch"
    set_version "$new_version"
    
    echo -e "${YELLOW}Bumped $bump_type version: $current_version → $new_version${NC}"
}

create_tag() {
    local current_version
    current_version=$(get_current_version)
    local tag="v$current_version"
    
    # Check if tag already exists
    if git tag -l | grep -q "^$tag$"; then
        echo -e "${YELLOW}Tag $tag already exists${NC}"
        return 0
    fi
    
    # Create tag
    git tag "$tag"
    echo -e "${GREEN}Created tag: $tag${NC}"
    echo -e "${YELLOW}To push tag: git push origin $tag${NC}"
}

# Main script logic
case "${1:-}" in
    current)
        echo "Current version: $(get_current_version)"
        ;;
    bump)
        if [[ -z "${2:-}" ]]; then
            echo -e "${RED}Error: Bump type required (major, minor, patch)${NC}"
            usage
            exit 1
        fi
        bump_version "$2"
        ;;
    set)
        if [[ -z "${2:-}" ]]; then
            echo -e "${RED}Error: Version required${NC}"
            usage
            exit 1
        fi
        set_version "$2"
        ;;
    tag)
        create_tag
        ;;
    help|--help|-h)
        usage
        ;;
    *)
        echo -e "${RED}Error: Unknown command${NC}"
        usage
        exit 1
        ;;
esac
