apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "zombie-ip-s3-ingest.fullname" . }}-test-connection"
  labels:
    {{- include "zombie-ip-s3-ingest.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "zombie-ip-s3-ingest.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
