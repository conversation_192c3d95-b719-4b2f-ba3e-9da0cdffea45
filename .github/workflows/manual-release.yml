name: Manual Release

on:
  workflow_dispatch:
    inputs:
      version:
        description: 'Version to release (e.g., 1.2.3)'
        required: true
        type: string
      release_type:
        description: 'Type of release'
        required: true
        default: 'release'
        type: choice
        options:
        - release
        - prerelease
        - hotfix

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  validate-version:
    runs-on: ubuntu-latest
    outputs:
      tag: ${{ steps.validate.outputs.tag }}
    steps:
    - name: Validate version format
      id: validate
      run: |
        VERSION="${{ github.event.inputs.version }}"
        
        # Validate semantic version format
        if [[ ! $VERSION =~ ^[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
          echo "Error: Version must be in semantic version format (e.g., 1.2.3)"
          exit 1
        fi
        
        TAG="v$VERSION"
        echo "tag=$TAG" >> $GITHUB_OUTPUT
        echo "Validated version: $VERSION (tag: $TAG)"

  build-and-push:
    needs: validate-version
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=raw,value=${{ github.event.inputs.version }}
          type=raw,value=latest,enable=${{ github.event.inputs.release_type == 'release' }}

    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        platforms: linux/amd64
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        build-args: |
          VERSION=${{ github.event.inputs.version }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  create-release:
    needs: [validate-version, build-and-push]
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Configure Git
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"

    - name: Create and push tag
      run: |
        TAG="${{ needs.validate-version.outputs.tag }}"
        git tag $TAG
        git push origin $TAG

    - name: Generate changelog
      id: changelog
      run: |
        LATEST_TAG=$(git describe --tags --abbrev=0 HEAD~1 2>/dev/null || echo "")
        if [ -z "$LATEST_TAG" ]; then
          COMMITS=$(git log --oneline --pretty=format:"- %s" HEAD)
        else
          COMMITS=$(git log ${LATEST_TAG}..HEAD --oneline --pretty=format:"- %s")
        fi
        
        echo "CHANGELOG<<EOF" >> $GITHUB_OUTPUT
        echo "$COMMITS" >> $GITHUB_OUTPUT
        echo "EOF" >> $GITHUB_OUTPUT

    - name: Create GitHub Release
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: ${{ needs.validate-version.outputs.tag }}
        release_name: Release ${{ needs.validate-version.outputs.tag }}
        body: |
          ## Manual Release - ${{ github.event.inputs.release_type }}
          
          Version: ${{ github.event.inputs.version }}
          
          ## Changes in this release
          
          ${{ steps.changelog.outputs.CHANGELOG }}
          
          ## Docker Image
          
          ```bash
          docker pull ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.event.inputs.version }}
          ```
        draft: false
        prerelease: ${{ github.event.inputs.release_type != 'release' }}
