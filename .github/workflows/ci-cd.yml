name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt

    - name: Run tests
      run: |
        # Add your test commands here
        python -m pytest tests/ || echo "No tests found, skipping..."

  version:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    outputs:
      version: ${{ steps.version.outputs.version }}
      tag: ${{ steps.version.outputs.tag }}
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
        token: ${{ secrets.GITHUB_TOKEN }}

    - name: Configure Git
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"

    - name: Determine version bump
      id: version
      run: |
        # Get the latest tag
        LATEST_TAG=$(git describe --tags --abbrev=0 2>/dev/null || echo "v0.0.0")
        echo "Latest tag: $LATEST_TAG"
        
        # Remove 'v' prefix for version calculation
        LATEST_VERSION=${LATEST_TAG#v}
        
        # Split version into parts
        IFS='.' read -ra VERSION_PARTS <<< "$LATEST_VERSION"
        MAJOR=${VERSION_PARTS[0]:-0}
        MINOR=${VERSION_PARTS[1]:-0}
        PATCH=${VERSION_PARTS[2]:-0}
        
        # Get commit messages since last tag
        COMMITS=$(git log ${LATEST_TAG}..HEAD --oneline --pretty=format:"%s" 2>/dev/null || git log --oneline --pretty=format:"%s")
        
        # Determine version bump based on commit messages
        BUMP_TYPE="patch"
        
        if echo "$COMMITS" | grep -qiE "^(feat|feature)(\(.+\))?!:|^.+!:|BREAKING CHANGE"; then
          BUMP_TYPE="major"
        elif echo "$COMMITS" | grep -qiE "^(feat|feature)(\(.+\))?:"; then
          BUMP_TYPE="minor"
        elif echo "$COMMITS" | grep -qiE "^(fix|bugfix|patch)(\(.+\))?:"; then
          BUMP_TYPE="patch"
        fi
        
        echo "Bump type: $BUMP_TYPE"
        
        # Calculate new version
        case $BUMP_TYPE in
          major)
            NEW_MAJOR=$((MAJOR + 1))
            NEW_MINOR=0
            NEW_PATCH=0
            ;;
          minor)
            NEW_MAJOR=$MAJOR
            NEW_MINOR=$((MINOR + 1))
            NEW_PATCH=0
            ;;
          patch)
            NEW_MAJOR=$MAJOR
            NEW_MINOR=$MINOR
            NEW_PATCH=$((PATCH + 1))
            ;;
        esac
        
        NEW_VERSION="$NEW_MAJOR.$NEW_MINOR.$NEW_PATCH"
        NEW_TAG="v$NEW_VERSION"
        
        echo "New version: $NEW_VERSION"
        echo "New tag: $NEW_TAG"
        
        # Set outputs
        echo "version=$NEW_VERSION" >> $GITHUB_OUTPUT
        echo "tag=$NEW_TAG" >> $GITHUB_OUTPUT
        
        # Create and push tag
        git tag $NEW_TAG
        git push origin $NEW_TAG

  build-and-push:
    needs: [test, version]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=semver,pattern={{version}},value=${{ needs.version.outputs.tag }}
          type=semver,pattern={{major}}.{{minor}},value=${{ needs.version.outputs.tag }}
          type=semver,pattern={{major}},value=${{ needs.version.outputs.tag }}
          type=raw,value=latest,enable={{is_default_branch}}

    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        platforms: linux/amd64
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        build-args: |
          VERSION=${{ needs.version.outputs.version }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  create-release:
    needs: [version, build-and-push]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Generate changelog
      id: changelog
      run: |
        LATEST_TAG=$(git describe --tags --abbrev=0 HEAD~1 2>/dev/null || echo "")
        if [ -z "$LATEST_TAG" ]; then
          COMMITS=$(git log --oneline --pretty=format:"- %s" HEAD)
        else
          COMMITS=$(git log ${LATEST_TAG}..HEAD --oneline --pretty=format:"- %s")
        fi
        
        echo "CHANGELOG<<EOF" >> $GITHUB_OUTPUT
        echo "$COMMITS" >> $GITHUB_OUTPUT
        echo "EOF" >> $GITHUB_OUTPUT

    - name: Create GitHub Release
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: ${{ needs.version.outputs.tag }}
        release_name: Release ${{ needs.version.outputs.tag }}
        body: |
          ## Changes in this release
          
          ${{ steps.changelog.outputs.CHANGELOG }}
          
          ## Docker Image
          
          ```bash
          docker pull ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ needs.version.outputs.version }}
          ```
        draft: false
        prerelease: false
